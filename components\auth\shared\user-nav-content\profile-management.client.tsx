// profile-management.tsx
'use client';
import { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../../../lib/utils';
import { useUnifiedSession } from '../../../../lib/auth-client';
import { useUserInitials } from '../../../../hooks/useAvatarUpdate';
import { AvatarUpload } from '../../../ui/avatar-upload.client';
import { getImageUrl } from '../../../../lib/utils/image';
import { ThemeBackgroundDialog, type Theme } from './theme-background-dialog.client';

import { 
  User, 
  Mail, 
  Shield, 
  AtSign,
  Edit3,
  Save,
  X,
  Check,
  Palette,
  Settings
} from 'lucide-react';

interface ProfileManagementProps {
  className?: string;
}

export function ProfileManagement({ className }: ProfileManagementProps) {
  const { session } = useUnifiedSession();
  const user = session?.user;
  const userInitials = useUserInitials(user?.name);

  // Process user image data
  const userImageUrl = getImageUrl(user?.image);
  
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(user?.name || '');
  const [isSaving, setIsSaving] = useState(false);

  // Theme dialog state with proper initialization
  const [isThemeDialogOpen, setIsThemeDialogOpen] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<Theme>('light');
  const [currentBackground, setCurrentBackground] = useState('default');

// Updated section of profile-management.tsx - replace the useEffect for theme initialization

// Initialize theme and background from localStorage
useEffect(() => {
  if (typeof window !== 'undefined') {
    const savedTheme = localStorage.getItem('theme') as Theme;
    const savedBackground = localStorage.getItem('background-theme');
    
    // Apply theme immediately on component mount
    const htmlElement = document.documentElement;
    
    function applyThemeToDOM(theme: Theme) {
      if (theme === 'dark') {
        htmlElement.classList.add('dark');
        htmlElement.classList.remove('light');
      } else if (theme === 'light') {
        htmlElement.classList.remove('dark');
        htmlElement.classList.add('light');
      } else {
        // System mode
        htmlElement.classList.remove('dark');
        htmlElement.classList.remove('light');
        
        // Check system preference
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (systemPrefersDark) {
          htmlElement.classList.add('dark');
        }
      }
    }
    
    // Set theme with proper fallback and immediate DOM application
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      setCurrentTheme(savedTheme);
      applyThemeToDOM(savedTheme);   
     
    }
    
    // Set background with proper fallback
    if (savedBackground) {
      setCurrentBackground(savedBackground);
    } else {
      localStorage.setItem('background-theme', 'default');
    }
    
    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = () => {
      const currentSavedTheme = localStorage.getItem('theme');
     
    };
    
    mediaQuery.addListener(handleSystemThemeChange);
    
    return () => {
      mediaQuery.removeListener(handleSystemThemeChange);
    };
  }
}, []);

  // Handle avatar update
  const handleAvatarUpdate = useCallback((imageUrl: string | null) => {
    console.log('Avatar updated:', imageUrl);
    // The avatar update hook will handle the actual database update
    // and refresh the session, so the UI will update automatically
  }, []);

  // Handle name edit
  const handleStartEdit = useCallback(() => {
    setEditedName(user?.name || '');
    setIsEditing(true);
  }, [user?.name]);

  const handleCancelEdit = useCallback(() => {
    setEditedName(user?.name || '');
    setIsEditing(false);
  }, [user?.name]);

  const handleSaveEdit = useCallback(async () => {
    if (!user || editedName.trim() === user.name) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    try {
      // TODO: Implement name update mutation
      console.log('Saving name update:', editedName.trim());
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update name:', error);
    } finally {
      setIsSaving(false);
    }
  }, [user, editedName]);

  // Handle theme changes with immediate application
  // Updated handleThemeChange in profile-management.tsx

const handleThemeChange = useCallback((theme: Theme) => {
  setCurrentTheme(theme);
  
  // Apply theme immediately to DOM
  const htmlElement = document.documentElement;
  
  function applyThemeToDOM(theme: Theme) {
    if (theme === 'dark') {
      htmlElement.classList.add('dark');
      htmlElement.classList.remove('light');
    } else if (theme === 'light') {
      htmlElement.classList.remove('dark');
      htmlElement.classList.add('light');
    } else {
      // System mode - remove explicit classes
      htmlElement.classList.remove('dark');
      htmlElement.classList.remove('light');
      
      // Apply system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (systemPrefersDark) {
        htmlElement.classList.add('dark');
      }
    }
  }
  
  applyThemeToDOM(theme);
  
  // Update localStorage immediately
  localStorage.setItem('theme', theme);
  
  // Dispatch event to notify other components
  window.dispatchEvent(new CustomEvent('themeChanged', {
    detail: { theme }
  }));
  
  console.log('Theme changed to:', theme, 'DOM classes:', htmlElement.classList.toString());
}, []);


  const handleBackgroundChange = useCallback((backgroundId: string) => {
    setCurrentBackground(backgroundId);
    
    // Update localStorage immediately
    localStorage.setItem('background-theme', backgroundId);
    
    // Dispatch event to update background in EnhancedSidebar
    window.dispatchEvent(new CustomEvent('backgroundThemeChanged', {
      detail: { backgroundId }
    }));
    
    console.log('Background changed to:', backgroundId);
  }, []);

  // Get current theme display name
  const getThemeDisplayName = useCallback((theme: Theme) => {
    switch (theme) {
      case 'light': return 'Light Mode';
      case 'dark': return 'Dark Mode';
      default: return 'System (Auto)';
    }
  }, []);

  // Get background display name
  const getBackgroundDisplayName = useCallback((backgroundId: string) => {
    const backgroundNames: Record<string, string> = {
      'default': 'Default',
      'horizon-glow-bottom': 'Horizon Glow (Bottom)',
      'crimson-depth-bottom': 'Crimson Depth (Bottom)',
      'emerald-void-bottom': 'Emerald Void (Bottom)',
      'violet-abyss-bottom': 'Violet Abyss (Bottom)',
      'azure-depths-bottom': 'Azure Depths (Bottom)',
      'orchid-depths-bottom': 'Orchid Depths (Bottom)',
      'horizon-glow-top': 'Horizon Glow (Top)',
      'crimson-depth-top': 'Crimson Depth (Top)',
      'emerald-void-top': 'Emerald Void (Top)',
      'violet-abyss-top': 'Violet Abyss (Top)',
      'azure-depths-top': 'Azure Depths (Top)',
      'orchid-depths-top': 'Orchid Depths (Top)'
    };
    return backgroundNames[backgroundId] || 'Default';
  }, []);

  if (!user) {
    return (
      <div className="p-4 h-full flex items-center justify-center">
        <p className="text-white/70">Loading profile...</p>
      </div>
    );
  }

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'student': return 'Student';
      case 'teacher': return 'Teacher';
      case 'school_admin': return 'School Administrator';
      default: return role;
    }
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'student': return 'text-blue-400';
      case 'teacher': return 'text-green-400';
      case 'school_admin': return 'text-orange-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <>
      <div className={cn("text-black/80 dark:text-white/80", className)}>
        {/* Avatar Section */}
        <div className="mb-6">
          <div className="flex flex-col items-center gap-4">
            <AvatarUpload
              currentImage={user.image}
              userInitials={userInitials}
              userName={user.name || user.email}
              onImageUpdate={handleAvatarUpdate}
              size="lg"
              className="mb-2"
            />
            <div className="text-center">
              <p className="text-sm font-redaction-normal text-black/70 dark:text-white/70 mb-1">Profile Picture</p>
              <p className="text-xs text-black/50 dark:text-white/50">Click or drag to upload a new avatar</p>
            </div>
          </div>
        </div>

        {/* User Information */}
        <div className="space-y-4">
          {/* Name Field */}
          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-black/70 dark:text-white/70" />
                <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Full Name</span>
              </div>
              {!isEditing && (
                <button
                  onClick={handleStartEdit}
                  className="p-1 hover:bg-black/10 dark:hover:bg-white/10 rounded transition-colors"
                >
                  <Edit3 className="h-3 w-3 text-black/70 dark:text-white/70" />
                </button>
              )}
            </div>
            
            {isEditing ? (
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  className="flex-1 font-manrope_1 placeholder:font-manrope_1 px-3 py-2 bg-black/10 dark:bg-white/10 border border-black/20 dark:border-white/20 rounded text-black dark:text-white placeholder-black/50 dark:placeholder-white/50 focus:outline-none focus:border-blue-400"
                  placeholder="Enter your full name"
                  disabled={isSaving}
                />
                <button
                  onClick={handleSaveEdit}
                  disabled={isSaving || editedName.trim() === user.name}
                  className="p-2 font-manrope_1 bg-green-500 hover:bg-green-600 disabled:bg-green-500/50 rounded transition-colors"
                >
                  {isSaving ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Save className="h-3 w-3 text-white" />
                    </motion.div>
                  ) : (
                    <Check className="h-3 w-3 text-white" />
                  )}
                </button>
                <button
                  onClick={handleCancelEdit}
                  disabled={isSaving}
                  className="p-2 font-manrope_1 bg-red-500 hover:bg-red-600 disabled:bg-red-500/50 rounded transition-colors"
                >
                  <X className="h-3 w-3 text-white" />
                </button>
              </div>
            ) : (
              <p className="text-black/80 dark:text-white/80 font-manrope_1 text-sm">{user.name}</p>
            )}
          </div>

          {/* Email Field */}
          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center gap-2 mb-2">
              <Mail className="h-4 w-4 text-black/70 dark:text-white/70" />
              <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Email Address</span>
            </div>
            <p className="text-black dark:text-white">{user.email}</p>
            <p className="text-xs text-black/50 dark:text-white/50 mt-1">Email cannot be changed</p>
          </div>

          {/* Username Field (for students) */}
          {user.role === 'student' && (user as any).username && (
            <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
              <div className="flex items-center gap-2 mb-2">
                <AtSign className="h-4 w-4 text-black/70 dark:text-white/70" />
                <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Username</span>
              </div>
              <p className="text-black/80 dark:text-white/80 font-manrope_1">{(user as any).username}</p>
              <p className="text-xs font-manrope_1 text-black/50 dark:text-white/50 mt-1">Username assigned by your teacher</p>
            </div>
          )}

          {/* Role Field */}
          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-black/70 dark:text-white/70" />
              <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Role</span>
            </div>
            <p className={cn("font-medium font-manrope_1 text-xs text-black/60 dark:text-white/60", getRoleColor(user.role))}>
              {getRoleDisplayName(user.role)}
            </p>
          </div>

          {/* Theme & Background Settings */}
          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Palette className="h-4 w-4 text-black/70 dark:text-white/70" />
                <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Theme & Background</span>
              </div>
              <button
                onClick={() => setIsThemeDialogOpen(true)}
                className="p-1 hover:bg-black/10 dark:hover:bg-white/10 rounded transition-colors"
              >
                <Settings className="h-3 w-3 text-black/70 dark:text-white/70" />
              </button>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-black/80 dark:text-white/80 font-manrope_1">Theme Mode</span>
                <span className="text-xs text-black/60 dark:text-white/60 font-manrope_1">{getThemeDisplayName(currentTheme)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-black/80 dark:text-white/80 font-manrope_1">Background</span>
                <span className="text-xs text-black/60 dark:text-white/60 font-manrope_1">{getBackgroundDisplayName(currentBackground)}</span>
              </div>
            </div>
            <p className="text-xs text-black/50 dark:text-white/50 mt-2">Click settings to customize your theme</p>
          </div>

          {/* Account Status */}
          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-redaction-normal font-medium text-black/80 dark:text-white/80 mb-1">Account Status</p>
                <p className="text-xs font-manrope_1 text-black/60 dark:text-white/60">
                  Created {new Date(user.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm font-manrope_1 text-green-400">Active</span>
              </div>
            </div>
          </div>

          {/* Additional sections for testing scroll */}
          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-black/70 dark:text-white/70" />
              <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Security Settings</span>
            </div>
            <p className="text-xs text-black/60 dark:text-white/60 mb-2">Manage your account security preferences</p>
            <div className="space-y-2">
              <button className="w-full text-left p-2 text-sm text-black/80 dark:text-white/80 hover:bg-black/5 dark:hover:bg-white/5 rounded transition-colors">
                Change Password
              </button>
              <button className="w-full text-left p-2 text-sm text-black/80 dark:text-white/80 hover:bg-black/5 dark:hover:bg-white/5 rounded transition-colors">
                Two-Factor Authentication
              </button>
              <button className="w-full text-left p-2 text-sm text-black/80 dark:text-white/80 hover:bg-black/5 dark:hover:bg-white/5 rounded transition-colors">
                Login History
              </button>
            </div>
          </div>

          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center gap-2 mb-2">
              <User className="h-4 w-4 text-black/70 dark:text-white/70" />
              <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Preferences</span>
            </div>
            <p className="text-xs text-black/60 dark:text-white/60 mb-2">Customize your experience</p>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2">
                <span className="text-sm text-black/80 dark:text-white/80">Dark Mode</span>
                <div className="w-10 h-6 bg-blue-500 rounded-full relative">
                  <div className="w-4 h-4 bg-white rounded-full absolute top-1 right-1 shadow-sm"></div>
                </div>
              </div>
              <div className="flex items-center justify-between p-2">
                <span className="text-sm text-black/80 dark:text-white/80">Email Notifications</span>
                <div className="w-10 h-6 bg-gray-300 dark:bg-gray-600 rounded-full relative">
                  <div className="w-4 h-4 bg-white rounded-full absolute top-1 left-1 shadow-sm"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Extra content to ensure scrolling is needed */}
          <div className="p-4 rounded-xl bg-gradient-to-b from-[#f8f8f8] via-[#f8f8f8] to-[#f0f0f0] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(229_229_229)_inset,0_0.5px_0_1.5px_#d4d4d8_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 overflow-hidden">
            <div className="flex items-center gap-2 mb-2">
              <User className="h-4 w-4 text-black/70 dark:text-white/70" />
              <span className="text-sm font-redaction-normal font-medium text-black/90 dark:text-white/90">Privacy Settings</span>
            </div>
            <p className="text-xs text-black/60 dark:text-white/60 mb-2">Control your privacy and data sharing</p>
            <div className="space-y-2">
              <button className="w-full text-left p-2 text-sm text-black/80 dark:text-white/80 hover:bg-black/5 dark:hover:bg-white/5 rounded transition-colors">
                Data Export
              </button>
              <button className="w-full text-left p-2 text-sm text-black/80 dark:text-white/80 hover:bg-black/5 dark:hover:bg-white/5 rounded transition-colors">
                Delete Account
              </button>
              <button className="w-full text-left p-2 text-sm text-black/80 dark:text-white/80 hover:bg-black/5 dark:hover:bg-white/5 rounded transition-colors">
                Privacy Policy
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Theme Background Dialog */}
      <ThemeBackgroundDialog
        isOpen={isThemeDialogOpen}
        onOpenChange={setIsThemeDialogOpen}
        currentTheme={currentTheme}
        currentBackground={currentBackground}
        onThemeChange={handleThemeChange}
        onBackgroundChange={handleBackgroundChange}
      />
    </>
  );
}