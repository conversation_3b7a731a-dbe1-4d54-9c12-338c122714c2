'use client';

import { useState } from 'react';
import { StudentManagementDialog } from './student-management-dialog.client';
import { Plus } from 'lucide-react';

interface StudentManagementDialogTriggerProps {
  className?: string;
  variant?: 'icon' | 'text' | 'button';
  children?: React.ReactNode;
}

export function StudentManagementDialogTrigger({
  className,
  variant = 'icon',
  children,
}: StudentManagementDialogTriggerProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <button onClick={() => setIsOpen(true)} className={className}>
        {variant === 'icon' ? <Plus size={18} /> : children}
      </button>
      <StudentManagementDialog
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        teacherClasses={[]}
      />
    </>
  );
}
