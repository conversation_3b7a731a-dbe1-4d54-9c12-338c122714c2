'use client';

import React, { useState, memo } from 'react';
import { useMutation } from 'blade/client/hooks';
import {StudentEditForm} from "../students/student-edit-form.client"
import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON><PERSON>rigger,
  <PERSON><PERSON>Content,
  TabsContents,
} from '../../../ui/animate-ui/components/tabs';
import { Avatar, AvatarFallback } from '../../../ui/avatar.client';
import { Image } from 'blade/client/components';
import { useUserInitials } from '../../../../hooks/useAvatarUpdate';
import { Users, UserPlus, UserX, User } from 'lucide-react';

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  teacherId?: string;
  createdAt?: string;
  image?: any; // StoredObject or string for avatar
}

interface Teacher {
  id: string;
  name: string;
  email: string;
  slug: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface StudentManagementTabsWithDataProps {
  students: Student[];
  removedStudents?: Student[];
  teacher: Teacher;
  availableGradeLevels?: GradeLevel[];
  teacherClasses?: ClassItem[];
}

// Helper function interfaces
interface CurrentStudentsTabProps {
  students: Student[];
  onEditStudent: (student: Student) => void;
  onRemoveStudent: (studentId: string) => void;
  onToggleStatus: (student: Student) => void;
  teacherClasses?: ClassItem[];
}

interface RemovedStudentsTabProps {
  removedStudents: Student[];
  onRestoreStudent: (studentId: string) => void;
}

// Current Students Tab Component
function CurrentStudentsTab({
  students,
  onEditStudent,
  onRemoveStudent,
  onToggleStatus,
  teacherClasses = []
}: CurrentStudentsTabProps) {
  if (students.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">No Students Yet</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          You haven't added any students yet. Use the sidebar dialog or the "Add New Student" tab to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold">Your Students</h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Manage your current students, edit their information, and assign them to classes.
          </p>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Student
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Login Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Grade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {students.map((student) => {
                const userInitials = useUserInitials(student.name || student.email || 'Student');



                return (
                  <tr key={student.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10 ring-2 ring-black/10 dark:ring-white/10">
                          {student.image && typeof student.image === 'object' ? (
                            <Image
                              src={student.image}
                              alt={student.name || 'Student'}
                              width={40}
                              height={40}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium">
                              {userInitials}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {student.name || 'Unnamed Student'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {student.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {student.username ? (
                          <>
                            <User className="h-4 w-4 text-blue-500" />
                            <span className="text-sm text-gray-900 dark:text-gray-100">Username</span>
                          </>
                        ) : (
                          <>
                            <Users className="h-4 w-4 text-green-500" />
                            <span className="text-sm text-gray-900 dark:text-gray-100">Email</span>
                          </>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {student.grade || 'Not assigned'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => onToggleStatus(student)}
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          student.isActive !== false
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}
                      >
                        {student.isActive !== false ? 'Active' : 'Inactive'}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => onEditStudent(student)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => onRemoveStudent(student.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          Remove
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Add Student Tab Component
function AddStudentTab({ teacherClasses = [] }: { teacherClasses?: ClassItem[] }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Add New Student</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Click the button below to open the student management dialog and add new students to your class.
        </p>
        <div className="space-y-4">
          {/* Direct button that opens the dialog - only used in this tab */}
          <button
            onClick={() => setIsDialogOpen(true)}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 px-4 py-2"
          >
            <UserPlus className="h-4 w-4" />
            Add New Student
          </button>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <h3 className="font-medium mb-2">Bulk Import</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Import multiple students from a CSV file
              </p>
              <button className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400">
                Coming Soon
              </button>
            </div>
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <h3 className="font-medium mb-2">Class Enrollment</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Enroll students in existing classes
              </p>
              <button className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400">
                Manage Classes
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Student Management Dialog - only rendered when this tab is active and dialog is open */}
      {/* <StudentManagementDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        teacherClasses={teacherClasses}
      /> */}
    </div>
  );
}

// Removed Students Tab Component
function RemovedStudentsTab({
  removedStudents,
  onRestoreStudent
}: RemovedStudentsTabProps) {
  if (removedStudents.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">No Removed Students</h2>
        <p className="text-gray-600 dark:text-gray-400">
          Students you remove from your class will appear here. You can restore them at any time.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold">Removed Students</h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Students who have been removed from your class. You can restore them at any time.
          </p>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Student
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Login Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Grade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Removed Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {removedStudents.map((student) => {
                const userInitials = useUserInitials(student.name || student.email || 'Student');
                return (
                  <tr key={student.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 opacity-60">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10 ring-2 ring-black/10 dark:ring-white/10 opacity-50">
                          {student.image && typeof student.image === 'object' ? (
                            <Image
                              src={student.image}
                              alt={student.name || 'Student'}
                              width={40}
                              height={40}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <AvatarFallback className="bg-gradient-to-br from-gray-400 to-gray-600 text-white font-medium">
                              {userInitials}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100 line-through">
                            {student.name || 'Unnamed Student'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 line-through">
                            {student.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2 opacity-50">
                        {student.username ? (
                          <>
                            <User className="h-4 w-4 text-blue-500" />
                            <span className="text-sm text-gray-900 dark:text-gray-100">Username</span>
                          </>
                        ) : (
                          <>
                            <Users className="h-4 w-4 text-green-500" />
                            <span className="text-sm text-gray-900 dark:text-gray-100">Email</span>
                          </>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 dark:text-gray-100 opacity-50">
                        {student.grade || 'Not assigned'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {student.createdAt ? new Date(student.createdAt).toLocaleDateString() : 'Unknown'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => onRestoreStudent(student.id)}
                        className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                      >
                        Restore
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

function StudentManagementTabsWithData({
  students: initialStudents,
  removedStudents: initialRemovedStudents = [],
  teacher,
  availableGradeLevels = [],
  teacherClasses = []
}: StudentManagementTabsWithDataProps) {


  const [activeTab, setActiveTab] = useState<'current' | 'add' | 'removed'>('current');
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [students, setStudents] = useState<Student[]>(initialStudents);
  const [removedStudents, setRemovedStudents] = useState<Student[]>(initialRemovedStudents);
  const [pendingOperations, setPendingOperations] = useState<Set<string>>(new Set());
  const { set } = useMutation();

  // PERFORMANCE FIX: Optimize student updates to prevent excessive revalidations
  React.useEffect(() => {
    // Skip if no initial students or if we have pending operations
    if (!initialStudents.length || pendingOperations.size > 0) {
      return;
    }

    // Don't update if we have pending operations for these students
    const filteredActiveStudents = initialStudents.filter(student =>
      !pendingOperations.has(student.id)
    );

    // More efficient change detection using JSON comparison for small arrays
    const currentStudentsJson = JSON.stringify(students.map(s => s.id).sort());
    const newStudentsJson = JSON.stringify(filteredActiveStudents.map(s => s.id).sort());

    if (currentStudentsJson !== newStudentsJson) {
      setStudents(filteredActiveStudents);
    }
  }, [initialStudents.length, pendingOperations.size]); // Use primitive values to reduce re-runs

  // PERFORMANCE FIX: Optimize removed students updates to prevent excessive revalidations
  React.useEffect(() => {
    // Skip if no removed students or if we have pending operations
    if (!initialRemovedStudents.length || pendingOperations.size > 0) {
      return;
    }

    // Don't update if we have pending operations for these students
    const filteredRemovedStudents = initialRemovedStudents.filter(student =>
      !pendingOperations.has(student.id)
    );

    // More efficient change detection using JSON comparison for small arrays
    const currentRemovedJson = JSON.stringify(removedStudents.map(s => s.id).sort());
    const newRemovedJson = JSON.stringify(filteredRemovedStudents.map(s => s.id).sort());

    if (currentRemovedJson !== newRemovedJson) {
      setRemovedStudents(filteredRemovedStudents);
    }
  }, [initialRemovedStudents.length, pendingOperations.size]); // Use primitive values to reduce re-runs

  // PERFORMANCE FIX: Optimize event listeners to prevent excessive revalidations
  React.useEffect(() => {
    // Only set up listeners if we have a teacher
    if (!teacher?.id) return;

    const handleStudentCreated = (event: CustomEvent) => {
      // Add the new student to the local state immediately for instant UI update
      const newStudent = event.detail.student;
      if (newStudent && teacher) {
        const studentData: Student = {
          id: newStudent.id,
          name: newStudent.name,
          email: newStudent.email,
          username: newStudent.username,
          grade: newStudent.grade,
          isActive: true,
          teacherId: teacher.id,
          createdAt: new Date().toISOString()
        };

        setStudents(prev => {
          // Prevent duplicate additions
          if (prev.some(s => s.id === studentData.id)) {
            return prev;
          }
          return [...prev, studentData].sort((a, b) =>
            (a.name || '').localeCompare(b.name || '')
          );
        });
      }
    };

    const handleStudentUpdated = (event: CustomEvent) => {
      // Update the student in the local state immediately for instant UI update
      const updatedStudent = event.detail.student;
      if (updatedStudent) {
        setStudents(prev => prev.map(student =>
          student.id === updatedStudent.id
            ? { ...student, ...updatedStudent }
            : student
        ));
      }
    };

    // Listen for custom events from the student creation dialog and edit form
    window.addEventListener('studentCreated', handleStudentCreated as EventListener);
    window.addEventListener('studentUpdated', handleStudentUpdated as EventListener);

    return () => {
      window.removeEventListener('studentCreated', handleStudentCreated as EventListener);
      window.removeEventListener('studentUpdated', handleStudentUpdated as EventListener);
    };
  }, [teacher?.id]); // Only depend on teacher ID to reduce re-runs

  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student);
  };

  const handleRemoveStudent = async (studentId: string) => {
    if (confirm('Are you sure you want to remove this student from your class?')) {
      try {
        // Mark as pending operation to prevent server data from overriding
        setPendingOperations(prev => new Set([...prev, studentId]));

        // Update the local state immediately for instant UI feedback
        setStudents(prev => prev.filter(s => s.id !== studentId));

        // Move the student to removed list immediately
        const studentToRemove = students.find(s => s.id === studentId);
        if (studentToRemove) {
          setRemovedStudents(prev => [...prev, { ...studentToRemove, isActive: false }]);
        }

        // Update the StudentTeacher relationship to inactive instead of modifying the user
        // This allows the student to remain active with other teachers
        await set.studentTeachers({
          with: {
            studentId: studentId,
            teacherId: teacher.id,
            status: 'active'
          },
          to: {
            status: 'inactive'
          }
        });

        // Clear pending operation after successful database update
        setPendingOperations(prev => {
          const newSet = new Set(prev);
          newSet.delete(studentId);
          return newSet;
        });


      } catch (error) {
        console.error('Error removing student:', error);

        // Clear pending operation and revert changes
        setPendingOperations(prev => {
          const newSet = new Set(prev);
          newSet.delete(studentId);
          return newSet;
        });

        // Revert the UI changes
        setStudents(initialStudents);
        setRemovedStudents(initialRemovedStudents);
        alert('Failed to remove student. Please try again.');
      }
    }
  };

  const handleRestoreStudent = async (studentId: string) => {
    try {
      // Mark as pending operation to prevent server data from overriding
      setPendingOperations(prev => new Set([...prev, studentId]));

      // Move student back to active list immediately
      const studentToRestore = removedStudents.find(s => s.id === studentId);
      if (studentToRestore) {
        setStudents(prev => [...prev, { ...studentToRestore, isActive: true }]);
        setRemovedStudents(prev => prev.filter(s => s.id !== studentId));
      }

      // Re-activate the StudentTeacher relationship
      await set.studentTeachers({
        with: {
          studentId: studentId,
          teacherId: teacher.id,
          status: 'inactive'
        },
        to: {
          status: 'active'
        }
      });

      // Clear pending operation after successful database update
      setPendingOperations(prev => {
        const newSet = new Set(prev);
        newSet.delete(studentId);
        return newSet;
      });


    } catch (error) {
      console.error('Error restoring student:', error);

      // Clear pending operation and revert changes
      setPendingOperations(prev => {
        const newSet = new Set(prev);
        newSet.delete(studentId);
        return newSet;
      });

      // Revert the UI changes
      setStudents(initialStudents);
      setRemovedStudents(initialRemovedStudents);
      alert('Failed to restore student. Please try again.');
    }
  };

  const handleToggleStudentStatus = async (student: Student) => {
    try {
      await set.users({
        with: { id: student.id },
        to: { isActive: !student.isActive }
      });
      // Update the local state to reflect the change immediately
      setStudents(prev => prev.map(s => 
        s.id === student.id ? { ...s, isActive: !s.isActive } : s
      ));
    } catch (error) {
      console.error('Error updating student status:', error);
      alert('Failed to update student status. Please try again.');
    }
  };

  return (
    <div>
      {/* Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'current' | 'add' | 'removed')}
        className="w-full bg-muted rounded-lg"
      >
        <TabsList
          className="grid w-full grid-cols-3 p-2 mb-6"
          activeClassName="rounded-full border border-black/10 dark:border-white/10 shadow-sm shadow-black/10 dark:shadow-white/10 bg-gradient-to-r from-[#e1dfdf] via-[#d8d7d7] to-[#dad8d8] dark:from-[#242427] dark:via-[#232325] dark:to-[#202023]"
        >
          <TabsTrigger
            value="current"
            className="flex h-9 rounded-full items-center justify-center px-3 py-1.5 text-xs font-manrope_1 text-black/40 dark:text-white/40 data-[state=active]:text-black/80 dark:data-[state=active]:text-white/80 transition-all"
          >
            <Users className="w-4 h-4 mr-2" />
            Current Students ({students.length})
          </TabsTrigger>
          <TabsTrigger
            value="add"
            className="flex h-9 rounded-full items-center justify-center px-3 py-1.5 text-xs font-manrope_1 text-black/40 dark:text-white/40 data-[state=active]:text-black/80 dark:data-[state=active]:text-white/80 transition-all"
          >
            <UserPlus className="w-4 h-4 mr-2" />
            Add New Student
          </TabsTrigger>
          <TabsTrigger
            value="removed"
            className="flex h-9 rounded-full items-center justify-center px-3 py-1.5 text-xs font-manrope_1 text-black/40 dark:text-white/40 data-[state=active]:text-black/80 dark:data-[state=active]:text-white/80 transition-all"
          >
            <UserX className="w-4 h-4 mr-2" />
            Removed Students ({removedStudents.length})
          </TabsTrigger>
        </TabsList>

        <TabsContents className="rounded-sm h-full bg-background">
          <TabsContent value="current" className="flex-1 overflow-y-auto h-full">
            <CurrentStudentsTab
              students={students}
              onEditStudent={handleEditStudent}
              onRemoveStudent={handleRemoveStudent}
              onToggleStatus={handleToggleStudentStatus}
              teacherClasses={teacherClasses}
            />
          </TabsContent>

          <TabsContent value="add" className="flex-1 overflow-y-auto h-full">
            <AddStudentTab teacherClasses={teacherClasses} />
          </TabsContent>

          <TabsContent value="removed" className="flex-1 overflow-y-auto h-full">
            <RemovedStudentsTab
              removedStudents={removedStudents}
              onRestoreStudent={handleRestoreStudent}
            />
          </TabsContent>
        </TabsContents>
      </Tabs>

      {/* Student Edit Modal */}
      {selectedStudent && (
        <StudentEditForm
          student={selectedStudent}
          isOpen={!!selectedStudent}
          onClose={() => setSelectedStudent(null)}
          availableGradeLevels={availableGradeLevels}
        />
      )}
    </div>
  );
}

// PERFORMANCE FIX: Export memoized component to prevent unnecessary re-renders
const MemoizedStudentManagementTabsWithData = memo(StudentManagementTabsWithData);

export default MemoizedStudentManagementTabsWithData;
