// pages/teacher/[slug]/students-optimized.tsx
import { use, useBatch } from 'blade/server/hooks';
import { useParams } from 'blade/hooks';
import TeacherStudentsPageWithData from '../../../components/teacher/TeacherStudentsPageWithData.client';

// Define types to fix implicit 'any' errors
interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface GradeLevel {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category: string;
  educationType: string;
  teacherId: string;
  sortOrder?: number;
  isActive?: boolean;
}

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface StudentTeacher {
  id: string;
  studentId: string;
  teacherId: string;
  assignedAt: string;
  status: string;
}

export default function OptimizedStudentsPage() {
  const { slug } = useParams();

  // OPTIMIZED: Single batched query instead of multiple separate queries
  // This prevents the excessive revalidation you're experiencing
  const [allUsers, allGradeLevels, allClasses, allStudentTeachers] = useBatch(() => [
    // Get ALL users (teachers and students) in one query
    use.users({
      where: {
        OR: [
          { role: 'teacher' },
          { role: 'student' }
        ]
      }
    }),
    
    // Get ALL grade levels
    use.gradeLevels({
      where: { isActive: true },
      orderedBy: { ascending: ['sortOrder', 'name'] }
    }),
    
    // Get ALL classes
    use.classes({
      where: { isActive: true }
    }),
    
    // Get ALL student-teacher relationships
    use.studentTeachers()
  ]);

  // Find the current teacher
  const teacher = allUsers.find((u: User) => u.slug === slug && u.role === 'teacher');

  if (!teacher) {
    return null; // Or a loading/error state
  }

  // Get active and inactive student relationships for this teacher
  const activeRelationships = allStudentTeachers.filter((st: StudentTeacher) =>
    st.teacherId === teacher.id && st.status === 'active'
  );
  const inactiveRelationships = allStudentTeachers.filter((st: StudentTeacher) =>
    st.teacherId === teacher.id && st.status === 'inactive'
  );

  // Get student user data for the relationships
  const activeStudents = activeRelationships
    .map((st: StudentTeacher) => allUsers.find((u: User) => u.id === st.studentId && u.role === 'student'))
    .filter((user: User | undefined): user is User => !!user)
    .sort((a: User, b: User) => (a.name || '').localeCompare(b.name || ''));

  const inactiveStudents = inactiveRelationships
    .map((st: StudentTeacher) => allUsers.find((u: User) => u.id === st.studentId && u.role === 'student'))
    .filter((user: User | undefined): user is User => !!user)
    .sort((a: User, b: User) => (a.name || '').localeCompare(b.name || ''));

  // Filter grade levels for this teacher
  const teacherGradeLevels = allGradeLevels.filter((grade: GradeLevel) => 
    grade.teacherId === teacher.id && grade.isActive !== false
  );

  // Filter classes for this teacher
  const teacherClasses = allClasses.filter((classItem: ClassItem) => 
    classItem.teacherId === teacher.id && classItem.isActive !== false
  );

  return (
    <TeacherStudentsPageWithData
      teacher={teacher}
      activeStudents={activeStudents}
      inactiveStudents={inactiveStudents}
      teacherGradeLevels={teacherGradeLevels}
      teacherClasses={teacherClasses}
    />
  );
}