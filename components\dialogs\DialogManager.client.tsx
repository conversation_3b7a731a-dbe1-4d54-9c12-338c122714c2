'use client';

import { useEffect, useRef } from 'react';
import { useAttention } from 'react-attention';
import { useDialogStore } from '../../lib/stores/dialog.store';
import { StudentManagementDialog } from '../auth/teacher/dialogs/student-management-dialog.client';
import { GradeLevelManagementDialog } from '../auth/teacher/dialogs/grade-level-management-dialog.client';

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface DialogManagerProps {
  teacherClasses?: ClassItem[];
}

// Hook for managing dialog state with react-attention
export function useDialogManager() {
  const {
    openStudentDialog,
    openGradeLevelDialog,
    closeAllDialogs,
  } = useDialogStore();

  return {
    openStudentDialog,
    openGradeLevelDialog,
    closeAllDialogs,
  };
}

export function DialogManager({ teacherClasses = [] }: DialogManagerProps) {
  const { activeDialog, closeAllDialogs } = useDialogStore();
  const studentDialogRef = useRef(null);
  const gradeLevelDialogRef = useRef(null);

  const studentDialogVisible = activeDialog === 'student';
  const gradeLevelDialogVisible = activeDialog === 'grade-level';

  // Use react-attention to ensure only one dialog is open at a time
  useAttention(studentDialogVisible, closeAllDialogs, studentDialogRef);
  useAttention(gradeLevelDialogVisible, closeAllDialogs, gradeLevelDialogRef);

  return (
    <>
      {/* Student Management Dialog */}
      <StudentManagementDialog
        isOpen={studentDialogVisible}
        onOpenChange={closeAllDialogs}
        teacherClasses={teacherClasses}
        ref={studentDialogRef}
      />
      
      {/* Grade Level Management Dialog */}
      <GradeLevelManagementDialog
        isOpen={gradeLevelDialogVisible}
        onOpenChange={closeAllDialogs}
        ref={gradeLevelDialogRef}
      />
    </>
  );
}