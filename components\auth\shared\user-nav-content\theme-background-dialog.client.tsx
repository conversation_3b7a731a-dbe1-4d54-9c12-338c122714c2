import React, { useState, useCallback, useRef, useEffect, useMemo, forwardRef } from 'react';
import { Dialog } from '@base-ui-components/react/dialog';
import { X, Palette, <PERSON>, <PERSON>, Check } from 'lucide-react';
import { cn } from '../../../../lib/utils';

// Define background themes with more visible light mode colors
export interface BackgroundTheme {
  id: string;
  name: string;
  lightStyle: React.CSSProperties;
  darkStyle: React.CSSProperties;
  preview: {
    light: string;
    dark: string;
  };
}

export const backgroundThemes: BackgroundTheme[] = [
  {
    id: 'default',
    name: 'Default',
    lightStyle: {
      background: "linear-gradient(to right, #f2f2f2, #e8e8e8, #eeeeee)"
    },
    darkStyle: {
      background: "linear-gradient(to right, #101012, #18181a, #171719)"
    },
    preview: {
      light: "linear-gradient(to right, #f2f2f2, #e8e8e8, #eeeeee)",
      dark: "linear-gradient(to right, #101012, #18181a, #171719)"
    }
  },
  {
    id: 'horizon-glow-bottom',
    name: 'Horizon Glow (Bottom)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 90%, #ffffff 30%, #bbdefb 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 90%, #000000 40%, #0d1a36 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 90%, #ffffff 30%, #bbdefb 100%)",
      dark: "radial-gradient(125% 125% at 50% 90%, #000000 40%, #0d1a36 100%)"
    }
  },
  {
    id: 'crimson-depth-bottom',
    name: 'Crimson Depth (Bottom)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 100%, #ffffff 30%, #ffcdd2 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 100%, #000000 40%, #2b0707 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 100%, #ffffff 30%, #ffcdd2 100%)",
      dark: "radial-gradient(125% 125% at 50% 100%, #000000 40%, #2b0707 100%)"
    }
  },
  {
    id: 'emerald-void-bottom',
    name: 'Emerald Void (Bottom)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 90%, #ffffff 30%, #c8e6c9 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 90%, #000000 40%, #072607 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 90%, #ffffff 30%, #c8e6c9 100%)",
      dark: "radial-gradient(125% 125% at 50% 90%, #000000 40%, #072607 100%)"
    }
  },
  {
    id: 'violet-abyss-bottom',
    name: 'Violet Abyss (Bottom)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 90%, #ffffff 30%, #e1bee7 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 90%, #000000 40%, #2b092b 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 90%, #ffffff 30%, #e1bee7 100%)",
      dark: "radial-gradient(125% 125% at 50% 90%, #000000 40%, #2b092b 100%)"
    }
  },
  {
    id: 'azure-depths-bottom',
    name: 'Azure Depths (Bottom)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 100%, #ffffff 30%, #c5cae9 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 100%, #000000 40%, #010133 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 100%, #ffffff 30%, #c5cae9 100%)",
      dark: "radial-gradient(125% 125% at 50% 100%, #000000 40%, #010133 100%)"
    }
  },
  {
    id: 'orchid-depths-bottom',
    name: 'Orchid Depths (Bottom)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 100%, #ffffff 30%, #f8bbd9 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 100%, #000000 40%, #350136 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 100%, #ffffff 30%, #f8bbd9 100%)",
      dark: "radial-gradient(125% 125% at 50% 100%, #000000 40%, #350136 100%)"
    }
  },
  {
    id: 'horizon-glow-top',
    name: 'Horizon Glow (Top)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #bbdefb 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #0d1a36 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #bbdefb 100%)",
      dark: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #0d1a36 100%)"
    }
  },
  {
    id: 'crimson-depth-top',
    name: 'Crimson Depth (Top)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #ffcdd2 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #2b0707 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #ffcdd2 100%)",
      dark: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #2b0707 100%)"
    }
  },
  {
    id: 'emerald-void-top',
    name: 'Emerald Void (Top)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #c8e6c9 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #072607 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #c8e6c9 100%)",
      dark: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #072607 100%)"
    }
  },
  {
    id: 'violet-abyss-top',
    name: 'Violet Abyss (Top)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #e1bee7 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #2b092b 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #e1bee7 100%)",
      dark: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #2b092b 100%)"
    }
  },
  {
    id: 'azure-depths-top',
    name: 'Azure Depths (Top)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #c5cae9 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #010133 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #c5cae9 100%)",
      dark: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #010133 100%)"
    }
  },
  {
    id: 'orchid-depths-top',
    name: 'Orchid Depths (Top)',
    lightStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #f8bbd9 100%)"
    },
    darkStyle: {
      background: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #350136 100%)"
    },
    preview: {
      light: "radial-gradient(125% 125% at 50% 10%, #ffffff 30%, #f8bbd9 100%)",
      dark: "radial-gradient(125% 125% at 50% 10%, #000000 40%, #350136 100%)"
    }
  }
];

export type Theme = 'light' | 'dark';

interface ThemeBackgroundDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  currentTheme?: Theme;
  currentBackground?: string;
  onThemeChange?: (theme: Theme) => void;
  onBackgroundChange?: (backgroundId: string) => void;
}

export const ThemeBackgroundDialog = forwardRef<HTMLDivElement, ThemeBackgroundDialogProps>(({
  isOpen,
  onOpenChange,
  currentTheme = 'light',
  currentBackground = 'default',
  onThemeChange,
  onBackgroundChange
}, ref) => {
  const [selectedTheme, setSelectedTheme] = useState<Theme>(currentTheme);
  const [selectedBackground, setSelectedBackground] = useState(currentBackground);
  const [isLoading, setIsLoading] = useState(false);

  // Update local state when props change
  useEffect(() => {
    setSelectedTheme(currentTheme);
    setSelectedBackground(currentBackground);
  }, [currentTheme, currentBackground]);

  // Determine if we're in dark mode for preview
  const isDarkMode = useMemo(() => {
    return selectedTheme === 'dark';
  }, [selectedTheme]);

  const handleThemeChange = useCallback((theme: Theme) => {
    setSelectedTheme(theme);
  }, []);

  const handleBackgroundChange = useCallback((backgroundId: string) => {
    setSelectedBackground(backgroundId);
  }, []);

  const handleSave = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Store in localStorage FIRST
      localStorage.setItem('theme', selectedTheme);
      localStorage.setItem('background-theme', selectedBackground);
      
      // Then call the callbacks (which will apply the changes immediately)
      onThemeChange?.(selectedTheme);
      onBackgroundChange?.(selectedBackground);
      
      // Close dialog
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save theme settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedTheme, selectedBackground, onThemeChange, onBackgroundChange, onOpenChange]);

  const getSelectedTheme = useCallback((): BackgroundTheme => {
    const theme = backgroundThemes.find(t => t.id === selectedBackground);
    if (theme) return theme;
    
    // Fallback to first theme or a default theme
    if (backgroundThemes.length > 0) return backgroundThemes[0]!;
    
    // Ultimate fallback if array is empty
    return {
      id: 'default',
      name: 'Default',
      lightStyle: { background: '#ffffff' },
      darkStyle: { background: '#000000' },
      preview: { light: '#ffffff', dark: '#000000' }
    };
  }, [selectedBackground]);

  const handleReset = useCallback(() => {
    setSelectedTheme(currentTheme);
    setSelectedBackground(currentBackground);
  }, [currentTheme, currentBackground]);

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 bg-black/50 opacity-100 transition-all duration-150 data-[ending-style]:opacity-0 data-[starting-style]:opacity-0 z-[100000]" />
        <Dialog.Popup 
          ref={ref} 
          className="fixed top-1/2 left-1/2 -mt-8 w-[96vw] md:max-w-[700px] max-w-[calc(100vw-3rem)] -translate-x-1/2 -translate-y-1/2 rounded-lg bg-gradient-to-r from-[#f2f2f2] via-[#e8e8e8] to-[#eeeeee] dark:from-[#101012] dark:via-[#18181a] dark:to-[#171719] text-gray-900 dark:text-gray-100 outline-1 outline-black/10 dark:outline-white/10 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 z-[100001] max-h-[85vh] overflow-hidden flex flex-col"
        >
          {/* Header */}
          <div className="flex-shrink-0 px-6 py-4 border-b border-black/10 dark:border-white/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Palette className="w-5 h-5 text-black/70 dark:text-white/70" />
                <h2 className="text-lg font-redaction-normal font-medium text-black/90 dark:text-white/90">
                  Theme & Background Settings
                </h2>
              </div>
              <Dialog.Close className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-none disabled:pointer-events-none">
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Dialog.Close>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 px-6 py-6 overflow-y-auto space-y-8">
            {/* Theme Mode Selection */}
            <div className="space-y-4">
              <h3 className="text-sm font-redaction-normal font-medium text-black/80 dark:text-white/80">
                Theme Mode
              </h3>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { value: 'light' as Theme, label: 'Light', icon: Sun },
                  { value: 'dark' as Theme, label: 'Dark', icon: Moon }
                ].map(({ value, label, icon: Icon }) => (
                  <button
                    key={value}
                    onClick={() => handleThemeChange(value)}
                    className={cn(
                      "p-4 rounded-xl border-2 transition-all duration-200 flex flex-col items-center gap-2 hover:scale-[1.02]",
                      selectedTheme === value
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-950/30 shadow-md"
                        : "border-black/10 dark:border-white/10 bg-white/50 dark:bg-black/20 hover:border-black/20 dark:hover:border-white/20"
                    )}
                  >
                    <Icon className={cn(
                      "w-6 h-6",
                      selectedTheme === value ? "text-blue-600 dark:text-blue-400" : "text-black/60 dark:text-white/60"
                    )} />
                    <span className={cn(
                      "text-sm font-manrope_1 font-medium",
                      selectedTheme === value ? "text-blue-700 dark:text-blue-300" : "text-black/80 dark:text-white/80"
                    )}>
                      {label}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* Background Selection */}
            <div className="space-y-4">
              <h3 className="text-sm font-redaction-normal font-medium text-black/80 dark:text-white/80">
                Background Theme
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {backgroundThemes.map((theme) => (
                  <button
                    key={theme.id}
                    onClick={() => handleBackgroundChange(theme.id)}
                    className={cn(
                      "group relative p-3 rounded-xl border-2 transition-all duration-200 hover:scale-[1.02]",
                      selectedBackground === theme.id
                        ? "border-blue-500 shadow-md"
                        : "border-black/10 dark:border-white/10 hover:border-black/20 dark:hover:border-white/20"
                    )}
                  >
                    {/* Preview */}
                    <div 
                      className="w-full h-20 rounded-lg border border-black/10 dark:border-white/10 mb-3 relative overflow-hidden"
                      style={isDarkMode ? { background: theme.preview.dark } : { background: theme.preview.light }}
                    >
                      {selectedBackground === theme.id && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="bg-white/90 dark:bg-black/90 rounded-full p-1">
                            <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {/* Label */}
                    <p className={cn(
                      "text-xs font-manrope_1 font-medium text-center",
                      selectedBackground === theme.id 
                        ? "text-blue-700 dark:text-blue-300" 
                        : "text-black/80 dark:text-white/80"
                    )}>
                      {theme.name}
                    </p>
                  </button>
                ))}
              </div>
            </div>

            {/* Preview Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-redaction-normal font-medium text-black/80 dark:text-white/80">
                Preview
              </h3>
              <div className="rounded-xl border border-black/10 dark:border-white/10 overflow-hidden">
                <div 
                  className="h-32 relative"
                  style={isDarkMode ? getSelectedTheme().darkStyle : getSelectedTheme().lightStyle}
                >
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className={cn(
                        "text-lg font-redaction-normal font-medium mb-1",
                        isDarkMode ? "text-white/90" : "text-black/90"
                      )}>
                        Preview
                      </div>
                      <div className={cn(
                        "text-sm font-manrope_1",
                        isDarkMode ? "text-white/70" : "text-black/70"
                      )}>
                        {backgroundThemes.find(t => t.id === selectedBackground)?.name} • {selectedTheme.charAt(0).toUpperCase() + selectedTheme.slice(1)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 px-6 py-4 border-t border-black/10 dark:border-white/10 bg-background">
            <div className="flex justify-between items-center">
              <div className="text-xs font-manrope_1 text-black/60 dark:text-white/60">
                Changes will be applied immediately
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleReset}
                  disabled={isLoading}
                  className="px-4 py-2 text-xs font-manrope_1 text-black/60 dark:text-white/60 hover:text-black/80 dark:hover:text-white/80 transition-colors"
                >
                  Reset
                </button>
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className={cn(
                    "px-4 py-2 text-xs font-manrope_1 rounded-md transition-colors",
                    isLoading
                      ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                      : "bg-blue-600 hover:bg-blue-700 text-white"
                  )}
                >
                  {isLoading ? 'Applying...' : 'Apply Changes'}
                </button>
              </div>
            </div>
          </div>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
});