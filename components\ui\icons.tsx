import React, { useState, useEffect, useRef } from 'react';
import { DateTime } from 'luxon';

// Base icon props interface
interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
}

// Standardized icon wrapper with consistent sizing
const IconWrapper: React.FC<{ children: React.ReactNode; size?: number; className?: string; viewBox?: string }> = ({ 
  children, 
  size = 24, 
  className = "", 
  viewBox = "0 0 24 24",
  ...props 
}) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width={size} 
    height={size} 
    viewBox={viewBox}
    className={`flex-shrink-0 ${className}`}
    {...props}
  >
    {children}
  </svg>
);

// FIXED: Dynamic Calendar Icon with date functionality
export const CalendarIcon = ({ size = 24, className = "", ...props }: IconProps) => {
  const [currentDate, setCurrentDate] = useState(() => DateTime.now());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastDateRef = useRef<string>(DateTime.now().toISODate());

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up interval to check for date changes
    intervalRef.current = setInterval(() => {
      const now = DateTime.now();
      const newDay = now.toISODate();
      
      // Only update state if the date actually changed
      if (lastDateRef.current !== newDay) {
        lastDateRef.current = newDay;
        setCurrentDate(now);
      }
    }, 60000); // Check every minute

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []); // Empty dependency array - only run once on mount

  const day = currentDate.day.toString();

  return (
    <IconWrapper size={size} className={className} {...props}>
      <path fill="currentColor" d="M18.435 4.955h-1.94v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-7v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-1.93a2.5 2.5 0 0 0-2.5 2.5v11a2.5 2.5 0 0 0 2.5 2.5h12.87a2.5 2.5 0 0 0 2.5-2.5v-11a2.5 2.5 0 0 0-2.5-2.5zm1.5 13.5c0 .83-.67 1.5-1.5 1.5H5.565c-.83 0-1.5-.67-1.5-1.5v-8.42h15.87v8.42zm0-9.42H4.065v-1.58c0-.83.67-1.5 1.5-1.5h1.93v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h7v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h1.94c.83 0 1.5.67 1.5 1.5v1.58z"/>      
      {/* Day number positioned in the calendar */}
      <text
        x="12"
        y="16.5"
        textAnchor="middle"
        className="fill-current text-[8px] font-medium"
        style={{ 
          fontFamily: 'system-ui, -apple-system, sans-serif',
          fontSize: '8px',
          fontWeight: '500'
        }}
      >
        {day}
      </text>
    </IconWrapper>
  );
};

// Alternative simpler solution - just update when component mounts
export const CalendarIconSimple = ({ size = 24, className = "", ...props }: IconProps) => {
  const [currentDate] = useState(() => DateTime.now());
  const day = currentDate.day.toString();

  return (
    <IconWrapper size={size} className={className} {...props}>
      <path fill="currentColor" d="M18.435 4.955h-1.94v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-7v-1.41c0-.26-.23-.51-.5-.5c-.27.01-.5.22-.5.5v1.41h-1.93a2.5 2.5 0 0 0-2.5 2.5v11a2.5 2.5 0 0 0 2.5 2.5h12.87a2.5 2.5 0 0 0 2.5-2.5v-11a2.5 2.5 0 0 0-2.5-2.5zm1.5 13.5c0 .83-.67 1.5-1.5 1.5H5.565c-.83 0-1.5-.67-1.5-1.5v-8.42h15.87v8.42zm0-9.42H4.065v-1.58c0-.83.67-1.5 1.5-1.5h1.93v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h7v.59c0 .26.23.51.5.5c.27-.01.5-.22.5-.5v-.59h1.94c.83 0 1.5.67 1.5 1.5v1.58z"/>      
      <text
        x="12"
        y="16.5"
        textAnchor="middle"
        className="fill-current text-[8px] font-medium"
        style={{ 
          fontFamily: 'system-ui, -apple-system, sans-serif',
          fontSize: '8px',
          fontWeight: '500'
        }}
      >
        {day}
      </text>
    </IconWrapper>
  );
};

// All your other icons remain the same...
export const TeacherIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="currentColor" d="M5 5h13a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8a3 3 0 0 1 3-3m0 1c-.5 0-.94.17-1.28.47l7.78 5.03l7.78-5.03C18.94 6.17 18.5 6 18 6H5m6.5 6.71L3.13 7.28C3.05 7.5 3 7.75 3 8v9a2 2 0 0 0 2 2h13a2 2 0 0 0 2-2V8c0-.25-.05-.5-.13-.72l-8.37 5.43Z"/>
  </IconWrapper>
);

export const SchoolIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="currentColor" d="M11 2.5L20 7v2H2V7l9-4.5m4 7.5h4v8h-4v-8M2 22v-3h18v3H2m7-12h4v8H9v-8m-6 0h4v8H3v-8m0 10v1h16v-1H3m1-9v6h2v-6H4m6 0v6h2v-6h-2m6 0v6h2v-6h-2M3 8h16v-.4l-8-4.02L3 7.6V8Z"/>
  </IconWrapper>
);

export const EmailIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="currentColor" d="M5 5h13a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V8a3 3 0 0 1 3-3m0 1c-.5 0-.94.17-1.28.47l7.78 5.03l7.78-5.03C18.94 6.17 18.5 6 18 6H5m6.5 6.71L3.13 7.28C3.05 7.5 3 7.75 3 8v9a2 2 0 0 0 2 2h13a2 2 0 0 0 2-2V8c0-.25-.05-.5-.13-.72l-8.37 5.43Z"/>
  </IconWrapper>
);

export const SearchIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="currentColor" d="M9.5 4a6.5 6.5 0 0 1 6.5 6.5c0 1.62-.59 3.1-1.57 4.23l5.65 5.65l-.71.71l-5.65-5.65A6.469 6.469 0 0 1 9.5 17A6.5 6.5 0 0 1 3 10.5A6.5 6.5 0 0 1 9.5 4m0 1A5.5 5.5 0 0 0 4 10.5A5.5 5.5 0 0 0 9.5 16a5.5 5.5 0 0 0 5.5-5.5A5.5 5.5 0 0 0 9.5 5Z"/>
  </IconWrapper>
);

export const CollapseBarIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path d="M2.75 6.5C2.75 4.42893 4.42893 2.75 6.5 2.75H17.5C19.5711 2.75 21.25 4.42893 21.25 6.5V17.5C21.25 19.5711 19.5711 21.25 17.5 21.25H6.5C4.42893 21.25 2.75 19.5711 2.75 17.5V6.5ZM6.5 4.25C5.25736 4.25 4.25 5.25736 4.25 6.5V17.5C4.25 18.7426 5.25736 19.75 6.5 19.75H8.25V4.25H6.5ZM17.5 19.75H9.75V4.25H17.5C18.7426 4.25 19.75 5.25736 19.75 6.5V17.5C19.75 18.7426 18.7426 19.75 17.5 19.75Z" fill="currentColor"/>
  </IconWrapper>
);

export const HouseIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M17 21H7a4 4 0 0 1-4-4v-6.292a4 4 0 0 1 1.927-3.421l5-3.03a4 4 0 0 1 4.146 0l5 3.03A4 4 0 0 1 21 10.707V17a4 4 0 0 1-4 4Zm-8-4h6"/>
  </IconWrapper>
);

export const StudentIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path d="M11.1093 1.90438C11.6584 1.58014 12.3406 1.57996 12.8896 1.90438L21.9238 7.24324C22.5024 7.58523 22.7878 8.17711 22.7822 8.76668V19.0313C22.7822 19.4455 22.4464 19.7813 22.0322 19.7813C21.618 19.7813 21.2822 19.4455 21.2822 19.0313V10.6348L19.749 11.5411V16.7247C19.749 16.8885 19.7168 17.0725 19.6269 17.252C19.2683 17.9685 17.3517 21.1495 11.999 21.1495C6.64628 21.1495 4.72971 17.9685 4.37106 17.252C4.28121 17.0725 4.24899 16.8885 4.24899 16.7247V11.5401L2.07419 10.2559C0.928423 9.57849 0.928368 7.92065 2.07419 7.24324L11.1093 1.90438ZM12.8896 15.5948C12.3406 15.9192 11.6584 15.9191 11.1093 15.5948L5.74899 12.4268V16.6505C6.05691 17.2212 7.61288 19.6495 11.999 19.6495C16.3851 19.6495 17.9411 17.2212 18.249 16.6505V12.4268L12.8896 15.5948ZM12.1259 3.19637C12.0475 3.15007 11.9505 3.15003 11.872 3.19637L2.83786 8.53426C2.67411 8.63102 2.6741 8.86816 2.83786 8.96492L11.872 14.3038C11.9503 14.3499 12.0477 14.3498 12.1259 14.3038L21.1611 8.96492C21.2404 8.91792 21.2799 8.83777 21.2822 8.75692V8.75008H21.2832C21.2833 8.66661 21.2429 8.58275 21.1611 8.53426L12.1259 3.19637Z" fill="currentColor"/>
  </IconWrapper>
);

export const StatusIcon = ({ size = 24, className = "", ...props }: IconProps) => (
  <IconWrapper size={size} className={className} {...props}>
    <path fillRule="evenodd" clipRule="evenodd" d="M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12Z" fill="currentColor"/>
    <path d="M12.5 18.4811C15.8562 18.2257 18.5 15.4216 18.5 12C18.5 8.57838 15.8562 5.77425 12.5 5.51894C12.2246 5.498 12 5.72386 12 6V18C12 18.2761 12.2246 18.502 12.5 18.4811Z" fill="currentColor"/>
  </IconWrapper>
);