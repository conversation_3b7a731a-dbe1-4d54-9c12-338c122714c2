'use client';

import { useState, useEffect } from 'react';
import { useLocation } from 'blade/hooks';
import { useDialogManager } from '../../../dialogs/DialogManager.client';
import { Popover } from '@base-ui-components/react/popover';
import { motion } from 'motion/react';
import { cn } from '../../../../lib/utils';
import {
  Plus,
  ChevronDown,
  UserPlus,
  GraduationCap,
  School,
  Settings,
  Users,
  BookOpen,
  Calendar,
  MoreHorizontal
} from 'lucide-react';

interface PageConfig {
  id: string;
  path: string;
  primaryAction: {
    label: string;
    icon: any;
    action: () => void;
    description: string;
  };
  secondaryActions: Array<{
    label: string;
    icon: any;
    action: () => void;
    description: string;
  }>;
}

export function DynamicHeader() {
  const location = useLocation();
  const { openStudentDialog, openGradeLevelDialog } = useDialogManager();
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Track theme changes for popover styling
  useEffect(() => {
    const checkTheme = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };

    checkTheme();
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  // Define page configurations
  const pageConfigs: PageConfig[] = [
    {
      id: 'students',
      path: '/students',
      primaryAction: {
        label: 'Add Student',
        icon: UserPlus,
        action: openStudentDialog,
        description: 'Invite new students to your classes'
      },
      secondaryActions: [
        {
          label: 'Create Grade Level',
          icon: GraduationCap,
          action: openGradeLevelDialog,
          description: 'Set up new grade levels for your students'
        },
        {
          label: 'Manage Classes',
          icon: School,
          action: () => {
            // Navigate to classes page - we'll implement this
            console.log('Navigate to classes');
          },
          description: 'View and manage your class settings'
        }
      ]
    },
    {
      id: 'classes',
      path: '/classes',
      primaryAction: {
        label: 'Create Grade Level',
        icon: GraduationCap,
        action: openGradeLevelDialog,
        description: 'Set up new grade levels and educational contexts'
      },
      secondaryActions: [
        {
          label: 'Add Students',
          icon: UserPlus,
          action: openStudentDialog,
          description: 'Invite students to your classes'
        },
        {
          label: 'Class Settings',
          icon: Settings,
          action: () => {
            console.log('Open class settings');
          },
          description: 'Configure class-specific settings'
        }
      ]
    },
    {
      id: 'calendar',
      path: '/calendar',
      primaryAction: {
        label: 'Add Event',
        icon: Calendar,
        action: () => {
          console.log('Add calendar event');
        },
        description: 'Schedule new events and activities'
      },
      secondaryActions: [
        {
          label: 'Add Students',
          icon: UserPlus,
          action: openStudentDialog,
          description: 'Invite students to your classes'
        },
        {
          label: 'Create Grade Level',
          icon: GraduationCap,
          action: openGradeLevelDialog,
          description: 'Set up new grade levels'
        }
      ]
    }
  ];

  // Get current page configuration
  const getCurrentPageConfig = (): PageConfig | null => {
    const pathname = location.pathname;
    
    for (const config of pageConfigs) {
      if (pathname.includes(config.path)) {
        return config;
      }
    }
    
    // Default fallback for home/dashboard
    return {
      id: 'dashboard',
      path: '',
      primaryAction: {
        label: 'Quick Add',
        icon: Plus,
        action: () => setIsPopoverOpen(true),
        description: 'Quick access to common actions'
      },
      secondaryActions: [
        {
          label: 'Add Students',
          icon: UserPlus,
          action: openStudentDialog,
          description: 'Invite students to your classes'
        },
        {
          label: 'Create Grade Level',
          icon: GraduationCap,
          action: openGradeLevelDialog,
          description: 'Set up new grade levels'
        }
      ]
    };
  };

  const currentConfig = getCurrentPageConfig();

  if (!currentConfig) {
    return null;
  }

  const { primaryAction, secondaryActions } = currentConfig;

  return (
    <div className="flex items-center gap-2">
      {/* Primary Action Button */}
      <button
        onClick={primaryAction.action}
        className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-manrope_1 bg-[#eeeef0] dark:bg-[#313035] text-black/80 dark:text-white/80 rounded-md hover:bg-[#fefefe] dark:hover:bg-[#3a3b40] transition-colors"
        title={primaryAction.description}
      >
        <primaryAction.icon className="w-3.5 h-3.5" />
        <span className="hidden sm:inline">{primaryAction.label}</span>
      </button>

      {/* Secondary Actions Popover */}
      {secondaryActions.length > 0 && (
        <Popover.Root open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
          <Popover.Trigger
            render={(props) => (
              <motion.button
                {...(props as any)}
                className="flex items-center gap-1 px-2 py-1.5 bg-[#eeeef0] dark:bg-[#313035] text-black/80 dark:text-white/80 select-none hover:bg-[#fefefe] dark:hover:bg-[#3a3b40] active:bg-[#fefefe] dark:active:bg-[#3a3b40] rounded-md transition-colors"
                animate={{
                  boxShadow: isPopoverOpen
                    ? isDarkMode
                      ? [
                          "inset 0 -1px 0 1px rgba(0,0,0,0.8)",
                          "inset 0 0 0 1px rgb(9,9,11)",
                          "inset 0 0.5px 0 1.5px #71717a"
                        ].join(", ")
                      : [
                          "inset 0 -1px 0 1px rgba(255,255,255,0.8)",
                          "inset 0 0 0 1px rgb(220,220,220)",
                          "inset 0 0.5px 0 1.5px #ccc"
                        ].join(", ")
                    : "none"
                }}
                transition={{
                  boxShadow: {
                    duration: 0.1,
                    ease: "easeInOut"
                  }
                }}
                title="More actions"
              >
                <MoreHorizontal className="w-3.5 h-3.5" />
                <ChevronDown className="w-3 h-3 opacity-60" />
              </motion.button>
            )}
          />
          <Popover.Portal>
            <Popover.Positioner sideOffset={8} align="end">
              <Popover.Popup className="origin-[var(--transform-origin)] rounded-xl bg-[#eeeef0] dark:bg-[#313035] px-2 py-2 text-black/90 dark:text-white/90 transition-[transform,scale,opacity] data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 min-w-[220px] shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]">
                
                <div className="space-y-1">
                  {secondaryActions.map((action, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        action.action();
                        setIsPopoverOpen(false);
                      }}
                      className="w-full flex font-manrope_1 items-center gap-3 px-3 py-2 rounded-md text-left transition-colors hover:bg-[#fefefe] dark:hover:bg-[#3a3b40]"
                    >
                      <action.icon className="w-4 h-4 flex-shrink-0 text-black/60 dark:text-white/60" />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-manrope_1 font-medium text-black/80 dark:text-white/80">
                          {action.label}
                        </div>
                        <div className="text-xs font-manrope_1 text-black/60 dark:text-white/60">
                          {action.description}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </Popover.Popup>
            </Popover.Positioner>
          </Popover.Portal>
        </Popover.Root>
      )}
    </div>
  );
}