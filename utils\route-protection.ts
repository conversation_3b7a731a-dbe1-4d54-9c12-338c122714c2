// utils/route-protection.ts
import React, { useEffect, useRef } from 'react';
import { useLocation, useRedirect } from 'blade/hooks';
import { useAuth } from '../hooks/useAuth';
import { useUnifiedSession } from '../lib/auth-client';
import type { RouteConfig, UserRole } from '../lib/types';

// Define your route configuration
export const routeConfig: RouteConfig[] = [
  // Public routes (accessible to everyone)
  { path: '/', type: 'public' },
  { path: '/login', type: 'public' },
  { path: '/test-sidebar', type: 'public' },

  // Protected routes with role restrictions
  { path: '/student', type: 'protected', allowedRoles: ['student'] },
  { path: '/teacher', type: 'protected', allowedRoles: ['teacher'] },
  { path: '/school', type: 'protected', allowedRoles: ['school_admin'] },
];

/**
 * Main route protection hook - handles all authentication and authorization
 * This replaces the individual auth wrappers and provides centralized auth logic
 */
export const useRouteProtection = () => {
  const location = useLocation();
  const redirect = useRedirect();
  const { user, _getLoadingState } = useAuth();
  const { refreshSession } = useUnifiedSession();
  const loading = _getLoadingState();
  const redirectRef = useRef(false);
  const sessionRefreshTriggered = useRef(false);
  const lastCheckedRef = useRef<{ path: string; userId: string | null; role: string | null }>({
    path: '',
    userId: null,
    role: null
  });

  useEffect(() => {
    // Reset redirect flag and session refresh flag when location changes
    redirectRef.current = false;
    sessionRefreshTriggered.current = false;
  }, [location.pathname]);

  useEffect(() => {
    // In Blade, we avoid loading states - perform redirects immediately
    // Only skip if we're genuinely still determining auth state
    if (loading) return;

    // Prevent multiple redirects
    if (redirectRef.current) return;

    // Add a small grace period for authentication transitions
    // This helps with Better Auth session cache issues after login
    const isRecentlyAuthenticated = () => {
      const lastAuthTime = sessionStorage.getItem('lastAuthTime');
      if (!lastAuthTime) return false;
      const timeDiff = Date.now() - parseInt(lastAuthTime);
      return timeDiff < 10000; // Increased to 10 second grace period for Better Auth session updates
    };

    const currentPath = location.pathname;
    const currentUserId = user?.id || null;
    const currentRole = user?.role || null;

    // Skip if we've already checked this exact combination to prevent Blade revalidation loops
    if (lastCheckedRef.current.path === currentPath &&
        lastCheckedRef.current.userId === currentUserId &&
        lastCheckedRef.current.role === currentRole) {
      return;
    }

    // Update the last checked state
    lastCheckedRef.current = { path: currentPath, userId: currentUserId, role: currentRole };

    const matchedRoute = findMatchingRoute(currentPath);

    if (!matchedRoute) {
      // No specific route config found, allow access
      return;
    }

    // Handle public routes (homepage, login)
    if (matchedRoute.type === 'public') {
      // Special handling for homepage and login - redirect authenticated users
      if (user && (currentPath === '/' || currentPath === '/login')) {
        const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
        redirectRef.current = true;
        redirect(`/${rolePrefix}/${user.slug || user.id}`);
      }
      // Other public routes like /test-sidebar are accessible to everyone
      return;
    }

    // Handle protected routes
    if (matchedRoute.type === 'protected') {
      // Remove frequent console.log to prevent spam - only log actual redirects

      // If there's no user, redirect to login
      if (!user) {
        // Check if we're already on login page to avoid redirect loops
        if (currentPath === '/login') {
          return;
        }

        // Check if we're in a recent authentication transition
        if (isRecentlyAuthenticated()) {
          console.log('Route protection - user not found but recently authenticated, waiting for session to update...');

          // Trigger a session refresh if we haven't already
          if (!sessionRefreshTriggered.current) {
            sessionRefreshTriggered.current = true;
            console.log('Route protection - triggering session refresh...');
            refreshSession().then(() => {
              console.log('Route protection - session refresh completed');
            }).catch((error) => {
              console.log('Route protection - session refresh failed:', error);
            });
          }

          return; // Give the session a moment to update
        }

        // Not authenticated - redirect to login with role hint if available
        const roleHint = matchedRoute.allowedRoles?.[0];
        const loginUrl = roleHint ? `/login?role=${roleHint}` : '/login';
        console.log('Route protection - redirecting unauthenticated user to:', loginUrl);
        redirectRef.current = true;
        redirect(loginUrl);
        return;
      }

      // Check role authorization
      if (matchedRoute.allowedRoles && !matchedRoute.allowedRoles.includes(user.role)) {
        // Check if we're in a recent authentication transition
        if (isRecentlyAuthenticated()) {
          console.log('Route protection - wrong role detected but recently authenticated, waiting for session to update...');
          console.log('Route protection - current user role:', user.role, 'required roles:', matchedRoute.allowedRoles);

          // Trigger a session refresh if we haven't already
          if (!sessionRefreshTriggered.current) {
            sessionRefreshTriggered.current = true;
            console.log('Route protection - triggering session refresh for role mismatch...');
            refreshSession().then(() => {
              console.log('Route protection - session refresh completed for role mismatch');
            }).catch((error) => {
              console.log('Route protection - session refresh failed for role mismatch:', error);
            });
          }

          return; // Give the session a moment to update
        }

        // Wrong role - redirect to user's correct area
        const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
        console.log('Route protection - wrong role, redirecting to:', `/${rolePrefix}/${user.slug || user.id}`);
        redirectRef.current = true;
        redirect(`/${rolePrefix}/${user.slug || user.id}`);
        return;
      }
    }
  }, [location.pathname, user, loading, redirect]);

  return { user, loading };
};

/**
 * Find the most specific route configuration for a given path
 */
function findMatchingRoute(path: string): RouteConfig | null {
  // Sort routes by specificity (longer paths first)
  const sortedRoutes = [...routeConfig].sort((a, b) => b.path.length - a.path.length);
  
  for (const route of sortedRoutes) {
    if (path.startsWith(route.path)) {
      return route;
    }
  }
  
  return null;
}

/**
 * Component wrapper for route protection
 * Following Blade's philosophy: instant redirects, no loading spinners
 * Use this to wrap specific components that need protection
 */
export const withRouteProtection = <P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles?: UserRole[]
) => {
  return function ProtectedComponent(props: P) {
    const { user, _getLoadingState } = useAuth();
    const loading = _getLoadingState();
    const redirect = useRedirect();
    
    // In Blade, we avoid loading states - redirect immediately when possible
    useEffect(() => {
      if (loading) return; // Only wait if genuinely loading
      
      // Check authentication
      if (!user) {
        const roleHint = allowedRoles?.[0];
        const loginUrl = roleHint ? `/login?role=${roleHint}` : '/login';
        redirect(loginUrl);
        return;
      }
      
      // Check role authorization
      if (allowedRoles && !allowedRoles.includes(user.role)) {
        const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
        redirect(`/${rolePrefix}/${user.slug}`);
        return;
      }
    }, [user, loading, redirect, allowedRoles]);
    
    // If we're still loading or about to redirect, return null (no flash)
    if (loading || !user || (allowedRoles && !allowedRoles.includes(user.role))) {
      return null;
    }
    
    return React.createElement(Component, props);
  };
};

/**
 * Hook for checking if current user can access a specific route
 */
export const useCanAccessRoute = (path: string): boolean => {
  const { user } = useAuth();
  const matchedRoute = findMatchingRoute(path);
  
  if (!matchedRoute) return true; // No restrictions
  
  if (matchedRoute.type === 'public') {
    return !user; // Public routes only for non-authenticated users
  }
  
  if (matchedRoute.type === 'protected') {
    if (!user) return false; // Must be authenticated
    
    if (matchedRoute.allowedRoles) {
      return matchedRoute.allowedRoles.includes(user.role);
    }
    
    return true; // Authenticated and no role restrictions
  }
  
  return true;
};

/**
 * Simple access control component that shows/hides content based on roles
 * Following Blade's philosophy: instant rendering, no loading states
 */
export const ProtectedContent: React.FC<{
  allowedRoles?: UserRole[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
}> = ({ allowedRoles, fallback = null, children }) => {
  const { user, canAccessRoles } = useAuth();
  
  if (!user) return fallback as React.ReactElement;
  
  if (allowedRoles && !canAccessRoles(allowedRoles)) {
    return fallback as React.ReactElement;
  }
  
  return children as React.ReactElement;
};