//command.client.tsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Command } from 'cmdk';
import { 
  Search, 
  User, 
  Calendar, 
  BookOpen, 
  Users, 
  Settings, 
  FileText, 
  Plus,
  Home,
  Bell,
  TrendingUp,
  Clock,
  Star,
  ChevronRight,
  FolderPlus,
  Folder,
  HelpCircle,
  MessageSquare
} from 'lucide-react';

// Sample data structure for your command menu
const commandData = {
  quickActions: [
    { id: 'new-note', label: 'New Note', icon: FileText, keywords: ['create', 'new', 'note'] },
    { id: 'new-folder', label: 'New Folder', icon: FolderPlus, keywords: ['create', 'new', 'folder'] },
    { id: 'settings', label: 'Settings', icon: Settings, keywords: ['settings', 'preferences'] },
  ],
  navigation: [
    { id: 'home', label: 'Dashboard', icon: Home, url: '', keywords: ['home', 'dashboard'] },
    { id: 'notes', label: 'Notes', icon: FileText, url: 'notes', keywords: ['notes', 'documents'] },
    { id: 'folders', label: 'Folders', icon: Folder, url: 'folders', keywords: ['folders', 'organize'] },
  ],
  recent: [
    { id: 'recent-note-1', label: 'Recent Note 1', icon: Clock, url: 'notes/recent-note-1', keywords: ['recent', 'note'] },
    { id: 'recent-note-2', label: 'Recent Note 2', icon: Clock, url: 'notes/recent-note-2', keywords: ['recent', 'note'] },
  ],
  suggestions: [
    { id: 'help', label: 'Help & Support', icon: HelpCircle, keywords: ['help', 'support'] },
    { id: 'feedback', label: 'Send Feedback', icon: MessageSquare, keywords: ['feedback', 'contact'] },
  ]
};

interface CommandMenuProps {
  onHeightChange?: (height: number) => void;
  onSelect?: (item: any) => void;
  onClose?: () => void;
  hideSearchInput?: boolean; // Hide the search input
  searchValue?: string; // External search value
  onSearchChange?: (value: string) => void; // External search change handler
}

export function CommandMenu({ 
  onHeightChange, 
  onSelect, 
  onClose,
  hideSearchInput = false,
  searchValue: externalSearchValue = '',
  onSearchChange
}: CommandMenuProps) {
  const [mounted, setMounted] = useState(false);
  const [internalSearchValue, setInternalSearchValue] = useState('');
  const resultsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const commandRef = useRef<HTMLDivElement>(null);

  // Use external search value if provided, otherwise use internal
  const searchValue = hideSearchInput ? externalSearchValue : internalSearchValue;

  useEffect(() => {
    setMounted(true);
  }, []);

  // CRITICAL: Focus management for immediate keyboard navigation
  useEffect(() => {
    if (mounted && commandRef.current) {
      // Use a more aggressive focus strategy
      const focusInput = () => {
        // First try to find the CMDK input directly
        const cmdkInput = commandRef.current?.querySelector('[cmdk-input]') as HTMLInputElement;
        if (cmdkInput) {
          cmdkInput.focus();
          console.log('✅ CMDK input focused successfully');
          return true;
        }
        
        // Fallback to any input inside the command
        const anyInput = commandRef.current?.querySelector('input') as HTMLInputElement;
        if (anyInput) {
          anyInput.focus();
          console.log('✅ Fallback input focused');
          return true;
        }
        
        // Last resort: focus the command root itself
        if (commandRef.current) {
          commandRef.current.focus();
          console.log('✅ Command root focused');
          return true;
        }
        
        return false;
      };

      // Try multiple times with increasing delays to ensure focus works
      const attempts = [0, 50, 100, 200];
      
      attempts.forEach((delay, index) => {
        setTimeout(() => {
          if (focusInput()) {
            // If we successfully focused, stop trying
            return;
          }
          if (index === attempts.length - 1) {
            console.warn('❌ Failed to focus command menu input after all attempts');
          }
        }, delay);
      });
    }
  }, [mounted]);

  // Monitor height changes of the results container
  useEffect(() => {
    if (!resultsRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const resultsHeight = entry.contentRect.height;
        // When hideSearchInput is true, we only report the results height
        // since the search input is handled externally
        const totalHeight = hideSearchInput ? resultsHeight : resultsHeight + 80;
        onHeightChange?.(totalHeight);
      }
    });

    resizeObserver.observe(resultsRef.current);
    return () => resizeObserver.disconnect();
  }, [onHeightChange, hideSearchInput]);

  const handleSelect = useCallback((value: string) => {
    const allItems = [
      ...commandData.quickActions,
      ...commandData.navigation,
      ...commandData.recent,
      ...commandData.suggestions
    ];
    
    const selectedItem = allItems.find(item => item.id === value);
    if (selectedItem && onSelect) {
      onSelect(selectedItem);
    }
    
    if (onClose) {
      onClose();
    }
  }, [onSelect, onClose]);

  // Handle internal search changes (when not using external input)
  const handleInternalSearchChange = (value: string) => {
    setInternalSearchValue(value);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="w-full flex flex-col">
      {/* Results Container - This is what ResizeObserver monitors */}
      <div 
        ref={resultsRef}
        className={`bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 shadow-lg overflow-hidden ${
          hideSearchInput ? 'rounded-lg' : 'rounded-t-lg border-b-0'
        }`}
        data-command-menu="true"
      >
        <Command 
          ref={commandRef}
          className="bg-transparent border-none"
          data-command-menu="true"
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              e.preventDefault();
              onClose?.();
            }
            // Let CMDK handle arrow keys naturally - don't interfere
          }}
        >
          {/* The actual CMDK input - positioned off-screen but focusable */}
          <Command.Input 
            ref={inputRef}
            placeholder="Type a command or search..."
            className="absolute -left-[9999px] w-px h-px opacity-0 pointer-events-none"
            value={searchValue}
            onValueChange={hideSearchInput ? onSearchChange : handleInternalSearchChange}
            autoFocus={true}
          />
          
          {/* Results area - with minimum height to prevent jumping */}
          <Command.List 
            className="overflow-y-auto custom-scrollbar p-2"
            style={{ 
              maxHeight: '300px',
              minHeight: '50px' // Ensures container never gets smaller than empty state
            }}
          >
            <Command.Empty className="flex flex-col items-center justify-center py-8 text-center">
             <div className="flex gap-2 items-center justify-center text-center">
                <Search className="w-4 h-4 text-zinc-400 dark:text-zinc-500 mb-2" />
              <p className="text-sm font-redaction-normal font-medium text-zinc-600 dark:text-zinc-400">
                No results found
              </p>
             </div>
            
              <p className="text-xs font-manrope text-zinc-500 dark:text-zinc-500 mt-1">
                Try a different search term
              </p>
            </Command.Empty>

            {/* Quick Actions Group */}
            <Command.Group heading="Quick Actions">
              {commandData.quickActions.map((item) => (
                <Command.Item
                  key={item.id}
                  value={item.id}
                  keywords={item.keywords}
                  onSelect={handleSelect}
                  className="flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800 mx-1 mb-1"
                >
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                    <item.icon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <span className="text-zinc-800 dark:text-zinc-200 font-medium">{item.label}</span>
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
                    Action
                  </div>
                </Command.Item>
              ))}
            </Command.Group>

            <Command.Separator className="h-px bg-zinc-200 dark:bg-zinc-700 my-2 mx-2" />

            {/* Navigation Group */}
            <Command.Group heading="Navigation">
              {commandData.navigation.map((item) => (
                <Command.Item
                  key={item.id}
                  value={item.id}
                  keywords={item.keywords}
                  onSelect={handleSelect}
                  className="flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800 mx-1 mb-1"
                >
                  <item.icon className="w-5 h-5 text-zinc-600 dark:text-zinc-400" />
                  <div className="flex-1">
                    <span className="text-zinc-800 dark:text-zinc-200">{item.label}</span>
                  </div>
                  <div className="text-xs text-zinc-600 dark:text-zinc-400 bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">
                    Page
                  </div>
                </Command.Item>
              ))}
            </Command.Group>

            <Command.Separator className="h-px bg-zinc-200 dark:bg-zinc-700 my-2 mx-2" />

            {/* Recent Group */}
            <Command.Group heading="Recent">
              {commandData.recent.map((item) => (
                <Command.Item
                  key={item.id}
                  value={item.id}
                  keywords={item.keywords}
                  onSelect={handleSelect}
                  className="flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800 mx-1 mb-1"
                >
                  <item.icon className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
                  <div className="flex-1">
                    <span className="text-zinc-800 dark:text-zinc-200">{item.label}</span>
                  </div>
                  <div className="text-xs text-zinc-600 dark:text-zinc-400 bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">
                    Recent
                  </div>
                </Command.Item>
              ))}
            </Command.Group>

            <Command.Separator className="h-px bg-zinc-200 dark:bg-zinc-700 my-2 mx-2" />

            {/* Suggestions Group */}
            <Command.Group heading="Suggestions">
              {commandData.suggestions.map((item) => (
                <Command.Item
                  key={item.id}
                  value={item.id}
                  keywords={item.keywords}
                  onSelect={handleSelect}
                  className="flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800 mx-1 mb-1"
                >
                  <item.icon className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
                  <div className="flex-1">
                    <span className="text-zinc-800 dark:text-zinc-200">{item.label}</span>
                  </div>
                  <div className="text-xs text-zinc-500 bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">
                    Tip
                  </div>
                </Command.Item>
              ))}
            </Command.Group>
          </Command.List>
        </Command>
      </div>

      {/* Only show the bottom search section if hideSearchInput is false */}
      {!hideSearchInput && (
        <div className="bg-zinc-50 dark:bg-zinc-900/50 border border-t-0 border-zinc-200 dark:border-zinc-700 rounded-b-lg shadow-lg">
          <div className="p-3">
            <div className="flex items-center space-x-2 mb-3">
              <Search className="h-4 w-4 text-zinc-400 dark:text-zinc-500" />
              <input
                type="text"
                placeholder="Type a command or search..."
                value={searchValue}
                onChange={(e) => hideSearchInput ? onSearchChange?.(e.target.value) : handleInternalSearchChange(e.target.value)}
                className="flex-1 bg-transparent border-none outline-none text-sm placeholder:text-zinc-500 dark:placeholder:text-zinc-400 text-zinc-900 dark:text-zinc-100"
                autoFocus
              />
            </div>
            
            {/* Keyboard Shortcuts */}
            <div className="flex items-center justify-between text-xs text-zinc-500 dark:text-zinc-400">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                    <span className="text-xs">↑</span>
                    <span className="text-xs">↓</span>
                  </kbd>
                  <span>navigate</span>
                </div>
                <div className="flex items-center space-x-1">
                  <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                    <span className="text-xs">↵</span>
                  </kbd>
                  <span>select</span>
                </div>
                <div className="flex items-center space-x-1">
                  <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                    <span className="text-xs">esc</span>
                  </kbd>
                  <span>close</span>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                  <span className="text-xs">⌘</span>
                </kbd>
                <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                  <span className="text-xs">K</span>
                </kbd>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}