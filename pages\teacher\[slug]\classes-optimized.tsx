// pages/teacher/[slug]/classes-optimized.tsx
import { use, useBatch } from 'blade/server/hooks';
import { useParams } from 'blade/hooks';
import TeacherClassesPageWithData from '../../../components/teacher/TeacherClassesPageWithData.client';
import { DialogManager } from '../../../components/dialogs/DialogManager.client';

export default function OptimizedClassesPage() {
  const { slug } = useParams();

  // OPTIMIZED: Single batched query for all class-related data
  const [allUsers, allClasses, allGradeLevels, allEducationalContexts] = useBatch(() => [
    // Get teachers to find current teacher
    use.users({
      where: { role: 'teacher' }
    }),
    
    // Get ALL classes
    use.classes({
      where: { isActive: true }
    }),
    
    // Get ALL grade levels
    use.gradeLevels({
      where: { isActive: true },
      orderedBy: { ascending: ['sortOrder', 'name'] }
    }),
    
    // Get ALL educational contexts
    use.educationalContexts({
      where: { isActive: true }
    })
  ]);

  // Find the current teacher
  const teacher = allUsers.find(u => u.slug === slug && u.role === 'teacher');
  
  // Filter classes for this teacher (for dialog)
  const teacherClasses = allClasses.filter(c => c.teacherId === teacher?.id);

  return (
    <>
      <TeacherClassesPageWithData
        allClasses={allClasses}
        allGradeLevels={allGradeLevels}
        allEducationalContexts={allEducationalContexts}
      />
      
      {/* Centralized Dialog Management with react-attention */}
      <DialogManager teacherClasses={teacherClasses} />
    </>
  );
}