// pages/teacher/[slug]/classes.tsx
import { use } from 'blade/server/hooks';
import TeacherClassesPageWithData from '../../../components/teacher/TeacherClassesPageWithData.client';

const TeachersClassesPage = () => {
  // PERFORMANCE FIX: Move use.* calls to top level to prevent excessive revalidations
  // Try to fetch data, but handle the case where models don't exist yet
  let allClasses: any[] = [];
  try {
    // Fetch classes with only needed fields to reduce data transfer
    allClasses = use.classes({
      selecting: ['id', 'name', 'teacherId', 'isActive', 'gradeLevel', 'subject', 'description', 'createdAt']
    }) || [];
  } catch (error) {
    // Model not yet migrated, silently use empty array
    allClasses = [];
  }

  let allGradeLevels: any[] = [];
  try {
    // Fetch grade levels with only needed fields to reduce data transfer
    allGradeLevels = use.gradeLevels({
      selecting: ['id', 'name', 'teacherId', 'isActive', 'educationalContext', 'createdAt']
    }) || [];
  } catch (error) {
    // Model not yet migrated, silently use empty array
    allGradeLevels = [];
  }

  let allEducationalContexts: any[] = [];
  try {
    // Fetch educational contexts with only needed fields to reduce data transfer
    allEducationalContexts = use.educationalContexts({
      selecting: ['id', 'name', 'teacherId', 'isActive', 'description', 'createdAt']
    }) || [];
  } catch (error) {
    // Model not yet migrated, silently use empty array
    allEducationalContexts = [];
  }

  return (
    <TeacherClassesPageWithData
      allClasses={allClasses}
      allGradeLevels={allGradeLevels}
      allEducationalContexts={allEducationalContexts}
    />
  );
};

export default TeachersClassesPage;
