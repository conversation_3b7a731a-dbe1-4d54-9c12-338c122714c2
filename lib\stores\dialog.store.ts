import { create } from 'zustand';

// Define the state and actions for the dialog store
interface DialogState {
  activeDialog: 'student' | 'grade-level' | null;
  openStudentDialog: () => void;
  openGradeLevelDialog: () => void;
  closeAllDialogs: () => void;
}

// Create the Zustand store
export const useDialogStore = create<DialogState>((set) => ({
  activeDialog: null,
  openStudentDialog: () => set({ activeDialog: 'student' }),
  openGradeLevelDialog: () => set({ activeDialog: 'grade-level' }),
  closeAllDialogs: () => set({ activeDialog: null }),
}));
