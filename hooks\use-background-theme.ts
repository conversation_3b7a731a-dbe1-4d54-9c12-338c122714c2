import { useState, useEffect, useCallback } from 'react';
import { backgroundThemes, type BackgroundTheme } from '../components/auth/shared/user-nav-content/theme-background-dialog.client';

export interface BackgroundThemeStyle {
  background: string;
}

export function useBackgroundTheme() {
  const [currentBackground, setCurrentBackground] = useState<string>('default');
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);

  // Initialize background
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setCurrentBackground('default');
    }
  }, []);

  // Listen for theme changes - simplified
  useEffect(() => {
    const updateTheme = () => {
      const htmlElement = document.documentElement;
      const hasDarkClass = htmlElement.classList.contains('dark');
      setIsDarkMode(hasDarkClass);
    };

    // Initial check
    updateTheme();

    // Listen for class changes on html element
    const observer = new MutationObserver(() => {
      updateTheme();
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  // Listen for background theme changes from other components
  useEffect(() => {
    const handleBackgroundThemeChange = (event: CustomEvent) => {
      const { backgroundId } = event.detail;
      setCurrentBackground(backgroundId);
    };

    window.addEventListener('backgroundThemeChanged', handleBackgroundThemeChange as EventListener);

    return () => {
      window.removeEventListener('backgroundThemeChanged', handleBackgroundThemeChange as EventListener);
    };
  }, []);

  // Get current background style
  const getBackgroundStyle = useCallback((): BackgroundThemeStyle => {
    const theme = backgroundThemes.find(t => t.id === currentBackground) || backgroundThemes[0];
    
    // Fallback if no theme is found
    if (!theme) {
      return { background: '' };
    }
         
    if (isDarkMode) {
      return { background: theme.darkStyle.background as string };
    } else {
      return { background: theme.lightStyle.background as string };
    }
  }, [currentBackground, isDarkMode]);

  // Get background className for Tailwind (fallback)
  const getBackgroundClassName = useCallback((): string => {
    if (currentBackground === 'default') {
      return "bg-gradient-to-r from-[#f2f2f2] via-[#e8e8e8] to-[#eeeeee] dark:from-[#101012] dark:via-[#18181a] dark:to-[#171719]";
    }
    
    // For custom backgrounds, we'll use inline styles instead
    return "";
  }, [currentBackground]);

  return {
    currentBackground,
    isDarkMode,
    backgroundStyle: getBackgroundStyle(),
    backgroundClassName: getBackgroundClassName(),
    setBackground: setCurrentBackground
  };
}